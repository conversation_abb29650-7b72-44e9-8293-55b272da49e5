<!doctype html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>tknucera - Styleguide in HTML/CSS/JS</title>

    <link rel="icon" href="../Assets/images/favicon.ico" type="image/x-icon"/>
    <link rel="shortcut icon" href="../Assets/images/favicon.ico" type="image/x-icon"/>

    <link rel="icon" type="image/gif" href="../Assets/images/favicon.gif"/>
    <link rel="icon" type="image/gif" href="../Assets/images/favicon.gif"/>

    <link rel="stylesheet" href="../Assets/styles/focuspoint.css" />
    <link rel="stylesheet" href="../Assets/styles/styles.css" />

    <!-- required for styleguide -->
    <link rel="stylesheet" href="../Assets/styles/prism.css" />
    <link rel="stylesheet" href="../Assets/styles/gridpreview.css" />
</head>
<body>

<!-- styleguide header -->
<header class="bg-primaryBlueOpacity text-[#ffffff] py-12 h-[40vh]">
    <div class="corporate_grid h-full">
        <div class="col-start-1 md:col-start-1 lg:col-start-1 col-end-5 md:col-end-6 lg:col-end-9 flex flex-col h-full justify-end">
            <div class="text-7xl mt-12 font-bold">Web UI Library</div>
            <div class="text-xl mt-4">Digital Corporate Design &ndash; Elemente und Komponenten</div>
        </div>
        <div class="col-start-1 row-start-1 md:col-start-7 lg:col-start-11 col-end-3 md:col-end-9 lg:col-end-13">
            <div class="p-8 bg-white w-full flex">
                <svg viewBox="0 0 292 120"><use href="../Assets/images/spritemap.svg#logo-tknucera" /></svg>
            </div>
        </div>
    </div>
</header>

<!-- styleguide content index -->
<nav class="py-12 bg-[rgba(0,0,0,0.8)] text-[#ffffff] min-h-[60vh]">
    <div class="corporate_grid">
        <div class="corporate_grid_full">
            <div id="contentIndex" class="mt-12 mb-8"></div>
        </div>
    </div>
</nav>

<!-- styleguide main -->
<main role="main" class="relative pb-8">

    <!-- color overview -->
    <section id="colorSection" class="corporate_section" data-section-info='
    {
        "title": "Colors",
        "description": "Farbsystem",
        "version": "1.0.0",
        "templates": ""
    }'>
        <div class="py-16">
            <div class="corporate_grid">
                <div class="corporate_grid_full">
                    <div class="h3 my-8">Primary</div>
                    <div class="colorList">
                        <div class="bg-primaryYellow"></div>
                        <div class="bg-primaryBlue"></div>
                    </div>
                </div>
                <div class="corporate_grid_full">
                    <div class="h3 my-8">Primary / Gradiations</div>
                    <div class="colorList">
                        <div class="bg-primaryYellow-110"></div>
                        <div class="bg-primaryYellow-80"></div>
                        <div class="bg-primaryYellow-60"></div>
                        <div class="bg-primaryYellow-40"></div>
                        <div class="bg-primaryYellow-20"></div>
                    </div>
                    <div class="colorList">
                        <div class="bg-primaryBlue-140"></div>
                        <div class="bg-primaryBlue-80"></div>
                        <div class="bg-primaryBlue-60"></div>
                        <div class="bg-primaryBlue-40"></div>
                        <div class="bg-primaryBlue-20"></div>
                        <div class="bg-primaryBlue-10"></div>
                    </div>
                </div>
                <div class="corporate_grid_full">
                    <div class="h3 my-8">Secondary</div>
                    <div class="colorList">
                        <div class="bg-black"></div>
                        <div class="bg-white"></div>
                    </div>
                </div>
                <div class="corporate_grid_full">
                    <div class="h3 my-8">Background</div>
                    <div class="colorList">
                        <div class="bg-darkGrey"></div>
                        <div class="bg-darkBlue"></div>
                        <div class="bg-softWhite"></div>
                    </div>
                </div>
                <div class="corporate_grid_full">
                    <div class="h3 my-8">Greytones</div>
                    <div class="colorList">
                        <div class="bg-grey-120"></div>
                        <div class="bg-grey-100"></div>
                        <div class="bg-grey-80"></div>
                        <div class="bg-grey-60"></div>
                        <div class="bg-grey-40"></div>
                        <div class="bg-grey-20"></div>
                        <div class="bg-grey-10"></div>
                        <div class="bg-grey-5"></div>

                    </div>
                </div>
                <div class="corporate_grid_full">
                    <div class="h3 my-8">Functional</div>
                    <div class="colorList">
                        <div class="bg-red"></div>
                        <div class="bg-lightRed"></div>
                        <div class="bg-green"></div>
                        <div class="bg-lightGreen"></div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- logo overview -->
    <section id="logoSection" class="corporate_section" data-section-info='
    {
        "title": "Logo",
        "description": "Logo und seine Größen",
        "version": "1.0.0",
        "templates": ""
    }'>
        <div class="py-16">
            <div class="corporate_grid">
                <div class="corporate_grid_full">
                    <div class="flex flex-row gap-12 flex-wrap items-center mt-24">
                        <div class="w-[38px] h-[16px]">
                            <svg viewBox="0 0 292 120"><use href="../Assets/images/spritemap.svg#logo-tknucera" /></svg>
                        </div>
                        <div class="w-[72px] h-[30px]">
                            <svg viewBox="0 0 292 120"><use href="../Assets/images/spritemap.svg#logo-tknucera" /></svg>
                        </div>
                        <div class="w-[121px] h-[50px]">
                            <svg viewBox="0 0 292 120"><use href="../Assets/images/spritemap.svg#logo-tknucera" /></svg>
                        </div>
                        <div class="w-[218px] h-[90px]">
                            <svg viewBox="0 0 292 120"><use href="../Assets/images/spritemap.svg#logo-tknucera" /></svg>
                        </div>
                        <div class="w-[291px] h-[120px]">
                            <svg viewBox="0 0 292 120"><use href="../Assets/images/spritemap.svg#logo-tknucera" /></svg>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- breadcrumb overview -->
    <section id="breadcrumbSection" class="corporate_section" data-section-info='
    {
        "title": "Breadcrumb",
        "description": "...",
        "version": "1.0.0",
        "templates": "_breadcrumb.html"
    }'>
        <div class="py-16">
            <div class="corporate_grid">
                <div class="corporate_grid_full">
                    <div class="placeTemplate" data-template="breadcrumb" data-component-title="Breadcrumb"></div>
                </div>
            </div>
        </div>
    </section>

    <!-- grid overview -->
    <section id="gridSection" class="corporate_section" data-section-info='
    {
        "title": "Grid",
        "description": "Grid mit 12 Spalten für Desktop, 8 Spalten für Tablets und 4 Spalten im mobile view.",
        "version": "1.0.0",
        "templates": "_grid.html"
    }'>
        <div class="py-16">
            <div class="placeTemplate" data-template="grid1col" data-component-title="1 Spalte"><h1>hello</h1></div>
            <div class="placeTemplate" data-template="grid2cols" data-component-title="2 Spalten"></div>
            <div class="placeTemplate" data-template="grid2cols_b" data-component-title="2 Spalten, 66/33"></div>
            <div class="placeTemplate" data-template="grid2cols_c" data-component-title="2 Spalten, 33/66"></div>
            <div class="placeTemplate" data-template="grid3cols" data-component-title="3 Spalten"></div>
            <div class="placeTemplate" data-template="grid4cols" data-component-title="4 Spalten"></div>
        </div>
    </section>

    <!-- headlines overview -->
    <section id="headlineSection" class="corporate_section" data-section-info='
    {
        "title": "Headlines",
        "description": "description",
        "version": "1.0.0",
        "templates": "_headlines.html"
    }'>
        <div class="py-16">
            <div class="corporate_grid">
                <div class="corporate_grid_full">
                    <div class="placeTemplate" data-template="h1" data-component-title="H1"></div>
                    <div class="placeTemplate" data-template="h1-blue" data-component-title="H1 Blue Text"></div>
                    <div class="placeTemplate" data-template="h1-blue-bg" data-component-title="H1 Blue Background"></div>
                    <div class="placeTemplate" data-template="h1-white-bg" data-component-title="H1 White Background"></div>

                    <div class="placeTemplate" data-template="h2" data-component-title="H2"></div>
                    <div class="placeTemplate" data-template="h2-blue" data-component-title="H2 Blue Text"></div>
                    <div class="placeTemplate" data-template="h2-blue-bg" data-component-title="H2 Blue Background"></div>
                    <div class="placeTemplate" data-template="h2-white-bg" data-component-title="H2 White Background"></div>

                    <div class="placeTemplate" data-template="h3" data-component-title="H3"></div>

                    <div class="placeTemplate" data-template="subtitle" data-component-title="Subtitle"></div>
                    <div class="placeTemplate" data-template="subtitle-bold" data-component-title="Subtitle Bold"></div>
                    <div class="placeTemplate" data-template="subtitle-black" data-component-title="Subtitle Black"></div>

                </div>
            </div>
        </div>
    </section>

    <!-- paragraph overview -->
    <section id="paragrahpSection" class="corporate_section" data-section-info='
    {
        "title": "Paragraphs",
        "description": "description",
        "version": "1.0.0",
        "templates": "_paragraphs.html"
    }'>
        <div class="py-16">
            <div class="corporate_grid">
                <div class="corporate_grid_full">
                    <div class="placeTemplate" data-template="p-big" data-component-title="Paragraph (big)"></div>
                    <div class="placeTemplate" data-template="p" data-component-title="Paragraph (normal)"></div>
                    <div class="placeTemplate" data-template="p-small" data-component-title="Paragraph (small)"></div>
                    <div class="placeTemplate" data-template="p-extrasmall" data-component-title="Paragraph (extrasmall)"></div>
                </div>
            </div>
        </div>
    </section>

    <!-- buttons overview -->
    <section id="buttonsSection" class="corporate_section" data-section-info='
    {
        "title": "Buttons",
        "status": "final",
        "description": "description",
        "version": "1.0.0",
        "templates": "_buttons.html"
    }'>
        <div class="py-16">
            <div class="corporate_grid">
                <div class="corporate_grid_full">
                    <div class="h3 my-16">Buttons Large</div>
                    <div class="flex flex-row gap-8 flex-wrap mb-8">
                        <div class="placeTemplate" data-template="button-large-solid" data-component-title="Button"></div>
                        <div class="placeTemplate" data-template="button-large-solid-checkmark" data-component-title="Button"></div>
                        <div class="placeTemplate" data-template="button-large-solid-arrow-right" data-component-title="Button"></div>
                        <div class="placeTemplate" data-template="button-large-solid-circle-arrow-right" data-component-title="Button"></div>
                    </div>
                    <div class="flex flex-row gap-8 flex-wrap mb-8">
                        <div class="placeTemplate" data-template="button-large-solid-disabled" data-component-title="Button"></div>
                        <div class="placeTemplate" data-template="button-large-solid-checkmark-disabled" data-component-title="Button"></div>
                        <div class="placeTemplate" data-template="button-large-solid-arrow-right-disabled" data-component-title="Button"></div>
                        <div class="placeTemplate" data-template="button-large-solid-circle-arrow-right-disabled" data-component-title="Button"></div>
                    </div>
                    <div class="flex flex-row gap-8 flex-wrap mb-8">
                        <div class="placeTemplate" data-template="button-large-white" data-component-title="Button"></div>
                        <div class="placeTemplate" data-template="button-large-white-checkmark" data-component-title="Button"></div>
                        <div class="placeTemplate" data-template="button-large-white-arrow-right" data-component-title="Button"></div>
                        <div class="placeTemplate" data-template="button-large-white-circle-arrow-right" data-component-title="Button"></div>
                    </div>
                    <div class="flex flex-row gap-8 flex-wrap mb-8">
                        <div class="placeTemplate" data-template="button-large-white-disabled" data-component-title="Button"></div>
                        <div class="placeTemplate" data-template="button-large-white-checkmark-disabled" data-component-title="Button"></div>
                        <div class="placeTemplate" data-template="button-large-white-arrow-right-disabled" data-component-title="Button"></div>
                        <div class="placeTemplate" data-template="button-large-white-circle-arrow-right-disabled" data-component-title="Button"></div>
                    </div>
                </div>
            </div>
            <div class="corporate_grid">
                <div class="corporate_grid_full">
                    <div class="h3 my-16">Buttons Medium</div>
                    <div class="flex flex-row gap-8 flex-wrap mb-8">
                        <div class="placeTemplate" data-template="button-medium-solid" data-component-title="Button"></div>
                        <div class="placeTemplate" data-template="button-medium-solid-checkmark" data-component-title="Button"></div>
                        <div class="placeTemplate" data-template="button-medium-solid-arrow-right" data-component-title="Button"></div>
                        <div class="placeTemplate" data-template="button-medium-solid-circle-arrow-right" data-component-title="Button"></div>
                    </div>
                    <div class="flex flex-row gap-8 flex-wrap mb-8">
                        <div class="placeTemplate" data-template="button-medium-solid-disabled" data-component-title="Button"></div>
                        <div class="placeTemplate" data-template="button-medium-solid-checkmark-disabled" data-component-title="Button"></div>
                        <div class="placeTemplate" data-template="button-medium-solid-arrow-right-disabled" data-component-title="Button"></div>
                        <div class="placeTemplate" data-template="button-medium-solid-circle-arrow-right-disabled" data-component-title="Button"></div>
                    </div>
                    <div class="flex flex-row gap-8 flex-wrap mb-8">
                        <div class="placeTemplate" data-template="button-medium-white" data-component-title="Button"></div>
                        <div class="placeTemplate" data-template="button-medium-white-checkmark" data-component-title="Button"></div>
                        <div class="placeTemplate" data-template="button-medium-white-arrow-right" data-component-title="Button"></div>
                        <div class="placeTemplate" data-template="button-medium-white-circle-arrow-right" data-component-title="Button"></div>
                    </div>
                    <div class="flex flex-row gap-8 flex-wrap mb-8">
                        <div class="placeTemplate" data-template="button-medium-white-disabled" data-component-title="Button"></div>
                        <div class="placeTemplate" data-template="button-medium-white-checkmark-disabled" data-component-title="Button"></div>
                        <div class="placeTemplate" data-template="button-medium-white-arrow-right-disabled" data-component-title="Button"></div>
                        <div class="placeTemplate" data-template="button-medium-white-circle-arrow-right-disabled" data-component-title="Button"></div>
                    </div>
                </div>
            </div>
            <div class="corporate_grid">
                <div class="corporate_grid_full">
                    <div class="h3 my-16">Buttons Small</div>
                    <div class="flex flex-row gap-8 flex-wrap mb-8">
                        <div class="placeTemplate" data-template="button-small-solid" data-component-title="Button"></div>
                        <div class="placeTemplate" data-template="button-small-solid-checkmark" data-component-title="Button"></div>
                        <div class="placeTemplate" data-template="button-small-solid-arrow-right" data-component-title="Button"></div>
                        <div class="placeTemplate" data-template="button-small-solid-circle-arrow-right" data-component-title="Button"></div>
                    </div>
                    <div class="flex flex-row gap-8 flex-wrap mb-8">
                        <div class="placeTemplate" data-template="button-small-solid-disabled" data-component-title="Button"></div>
                        <div class="placeTemplate" data-template="button-small-solid-checkmark-disabled" data-component-title="Button"></div>
                        <div class="placeTemplate" data-template="button-small-solid-arrow-right-disabled" data-component-title="Button"></div>
                        <div class="placeTemplate" data-template="button-small-solid-circle-arrow-right-disabled" data-component-title="Button"></div>
                    </div>
                    <div class="flex flex-row gap-8 flex-wrap mb-8">
                        <div class="placeTemplate" data-template="button-small-white" data-component-title="Button"></div>
                        <div class="placeTemplate" data-template="button-small-white-checkmark" data-component-title="Button"></div>
                        <div class="placeTemplate" data-template="button-small-white-arrow-right" data-component-title="Button"></div>
                        <div class="placeTemplate" data-template="button-small-white-circle-arrow-right" data-component-title="Button"></div>
                    </div>
                    <div class="flex flex-row gap-8 flex-wrap mb-8">
                        <div class="placeTemplate" data-template="button-small-white-disabled" data-component-title="Button"></div>
                        <div class="placeTemplate" data-template="button-small-white-checkmark-disabled" data-component-title="Button"></div>
                        <div class="placeTemplate" data-template="button-small-white-arrow-right-disabled" data-component-title="Button"></div>
                        <div class="placeTemplate" data-template="button-small-white-circle-arrow-right-disabled" data-component-title="Button"></div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- images overview -->
    <section id="imageSection" class="corporate_section" data-section-info='
    {
        "title": "Images",
        "description": "description",
        "version": "1.0.0",
        "templates": "_images.html"
    }'>
        <div class="py-16">
            <div class="corporate_grid">
                <div class="corporate_grid_halfLeft">
                    <div class="placeTemplate" data-template="image" data-component-title="Image (original ratio)"></div>
                    <div class="placeTemplate" data-template="image-1x1" data-component-title="Image (1x1 ratio)"></div>
                    <div class="placeTemplate" data-template="image-4x3" data-component-title="Image (4x3 ratio)"></div>
                    <div class="placeTemplate" data-template="image-3x2" data-component-title="Image (3x2 ratio)"></div>
                    <div class="placeTemplate" data-template="image-3x1" data-component-title="Image (3x1 ratio)"></div>
                    <div class="placeTemplate" data-template="image-16x10" data-component-title="Image (16x10 ratio)"></div>
                    <div class="placeTemplate" data-template="image-16x9" data-component-title="Image (16x9 ratio)"></div>
                </div>
                <div class="corporate_grid_halfRight">
                    <div class="placeTemplate" data-template="image-2x1" data-component-title="Image (2x1 ratio)"></div>
                    <div class="placeTemplate" data-template="image-5x2" data-component-title="Image (5x2 ratio)"></div>
                    <div class="placeTemplate" data-template="image-golden" data-component-title="Image (golden ratio)"></div>
                    <div class="placeTemplate" data-template="image-2x3" data-component-title="Image (2x3 ratio)"></div>
                    <div class="placeTemplate" data-template="image-3x4" data-component-title="Image (3x4 ratio)"></div>
                </div>
            </div>
        </div>
    </section>

    <!-- paragraph overview -->
    <section id="heroSection" class="corporate_section" data-section-info='
    {
        "title": "Hero",
        "description": "description",
        "version": "1.0.0",
        "templates": "_hero.html"
    }'>
        <div class="py-16">
            <div class="placeTemplate" data-template="hero" data-component-title="Hero"></div>
            <div class="placeTemplate" data-template="hero-search" data-component-title="Hero Search"></div>
        </div>
    </section>

    <!-- accordeon overview -->
    <section id="accordeonSection" class="corporate_section" data-section-info='
    {
        "title": "Accordeon",
        "status": "final",
        "description": "description",
        "version": "1.0.0",
        "templates": "_accordeon.html"
    }'>
        <div class="py-16">
            <div class="corporate_grid">
                <div class="corporate_grid_full">
                    <div class="placeTemplate" data-template="accordeon" data-component-title="Accordeon"></div>
                </div>
            </div>
        </div>
    </section>

    <!-- accordeon overview -->
    <section id="alertboxSection" class="corporate_section" data-section-info='
    {
        "title": "Hinweis-Kästen",
        "status": "final",
        "description": "description",
        "version": "1.0.0",
        "templates": "_alerts.html"
    }'>
        <div class="py-16">
            <div class="corporate_grid">
                <div class="corporate_grid_full">
                    <div class="placeTemplate" data-template="success-note" data-component-title="Hinweis-Kasten"></div>
                    <div class="placeTemplate" data-template="success-note-2" data-component-title="Hinweis-Kasten"></div>
                    <div class="placeTemplate" data-template="error-note" data-component-title="Hinweis-Kasten"></div>
                </div>
            </div>
        </div>
    </section>




    <!-- TODO: final markup for the sections below -->

    <!-- cards overview -->
    <section id="cardsSection" class="corporate_section" data-section-info='
    {
        "title": "Cards",
        "status": "draft",
        "description": "description",
        "version": "1.0.0",
        "templates": "_cards.html"
    }'>
        <div class="py-16">
            <div class="corporate_grid">
                <div class="corporate_grid_full">
                    <div class="placeTemplate" data-template="simpleCard" data-component-title="Card"></div>
                </div>
            </div>
            <div class="placeTemplate" data-template="cardTeaserRow" data-component-title="Card Teaser Reihe"></div>
        </div>
    </section>

    <!-- cards overview -->
    <section id="layoutSection" class="corporate_section" data-section-info='
    {
        "title": "Layouts",
        "status": "draft",
        "description": "description",
        "version": "1.0.0",
        "templates": "_layouts.html"
    }'>
        <div class="py-16">
            <div class="placeTemplate" data-template="layout-1" data-component-title="Layout 1"></div>
            <div class="placeTemplate" data-template="layout-2" data-component-title="Layout 2"></div>
            <div class="placeTemplate" data-template="layout-3" data-component-title="Layout 3"></div>
            <div class="placeTemplate" data-template="layout-4" data-component-title="Layout 4"></div>
            <div class="placeTemplate" data-template="layout-5" data-component-title="Layout 5"></div>
        </div>
    </section>

    <div id="pageOverlay" class="hidden absolute left-0 top-0 w-full h-full bg-[rgba(255,255,255,0.9)] z-10"></div>
</main>

<script src="../Assets/scripts/focuspoint.js"></script>
<script src="../Assets/scripts/functions.js"></script>

<!-- styleguide footer -->
<footer class="bg-[#333333] text-[#ffffff] min-h-[400px] py-12">
    <div class="corporate_grid">
        <div class="corporate_grid_full">
            footer
        </div>
    </div>
</footer>

<div id="codeViewer" class="hidden bg-primaryBlue pt-12 fixed w-full h-[50%] bottom-0 z-[200]"></div>
<div id="gridPreview"><div class="previewRow"><div class="previewCol previewCol-1"></div><div class="previewCol previewCol-2"></div><div class="previewCol previewCol-3"></div><div class="previewCol previewCol-4"></div><div class="previewCol previewCol-5"></div><div class="previewCol previewCol-6"></div><div class="previewCol previewCol-7"></div><div class="previewCol previewCol-8"></div><div class="previewCol previewCol-9"></div><div class="previewCol previewCol-10"></div><div class="previewCol previewCol-11"></div><div class="previewCol previewCol-12"></div></div><div id="middleAxis"></div></div>


<!-- styleguide templates starts -->
<template id="sectionHeader"
><div class="sticky top-0 z-[9] w-full bg-primaryBlueOpacity text-white py-8">
    <div class="corporate_grid">
        <div class="corporate_grid_full flex gap-4 items-center">
            <div class="text-[24px] px-3 py-1 bg-white inline-block -ml-3 text-primaryBlue border border-primaryBlue">
                <strong>###title###</strong>
                <span class="pl-2 text-[14px] text-[#555555]">###version###</span>
            </div>
            <div class="text-[20px]">###description###</div>
        </div>
    </div>
</div></template>
<!-- styleguide templates end -->
<div class="hidden">
    <svg fill="none" xmlns="http://www.w3.org/2000/svg">
        <symbol id="code" viewBox="0 0 15 15"><path d="M10.1464 10.1464L9.79289 10.5L10.5 11.2071L10.8536 10.8536L10.1464 10.1464ZM13.5 7.5L13.8536 7.85355L14.2071 7.5L13.8536 7.14645L13.5 7.5ZM10.8536 4.14645L10.5 3.79289L9.79289 4.5L10.1464 4.85355L10.8536 4.14645ZM4.14645 10.8536L4.5 11.2071L5.20711 10.5L4.85355 10.1464L4.14645 10.8536ZM1.5 7.5L1.14645 7.14645L0.792893 7.5L1.14645 7.85355L1.5 7.5ZM4.85355 4.85355L5.20711 4.5L4.5 3.79289L4.14645 4.14645L4.85355 4.85355ZM10.8536 10.8536L13.8536 7.85355L13.1464 7.14645L10.1464 10.1464L10.8536 10.8536ZM13.8536 7.14645L10.8536 4.14645L10.1464 4.85355L13.1464 7.85355L13.8536 7.14645ZM4.85355 10.1464L1.85355 7.14645L1.14645 7.85355L4.14645 10.8536L4.85355 10.1464ZM1.85355 7.85355L4.85355 4.85355L4.14645 4.14645L1.14645 7.14645L1.85355 7.85355ZM8.0068 1.4178L6.0068 13.4178L6.9932 13.5822L8.9932 1.5822L8.0068 1.4178Z" fill="#000000"/></symbol>
    </svg>
</div>
<script src="../Assets/scripts/prism.js"></script>
<script src="../Assets/scripts/app.js"></script>

</body>
</html>

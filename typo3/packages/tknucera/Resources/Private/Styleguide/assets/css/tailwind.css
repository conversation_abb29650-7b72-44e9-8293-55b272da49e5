@layer base {
    @font-face {
        font-family: 'TKType';
        font-style: normal;
        font-weight: normal;
        font-display: swap;
        src: url('../fonts/TKTypeRegular.woff2') format('woff2'),
        url('../fonts/TKTypeRegular.woff') format('woff');
    }
    @font-face {
        font-family: 'TKTypeBold';
        font-style: normal;
        font-weight: bold;
        font-display: swap;
        src: url('../fonts/TKTypeBold.woff2') format('woff2'),
        url('../fonts/TKTypeBold.woff') format('woff');
    }
    @font-face {
        font-family: 'TKTypeMedium';
        font-style: normal;
        font-weight: medium;
        font-display: swap;
        src: url('../fonts/TKTypeMedium.woff2') format('woff2'),
        url('../fonts/TKTypeMedium.woff') format('woff');
    }
    @font-face {
        font-family: 'Orbitron-Bold';
        font-style: normal;
        font-weight: bold;
        font-display: swap;
        src: url('../fonts/Orbitron-Bold.woff2') format('woff2'),
        url('../fonts/Orbitron-Bold.woff') format('woff');
    }
}

@layer utilities {
    .font-small {
        font-size: 75%;
    }
    .font-large {
        font-size: 120%;
    }
}

@layer components {
    body {
        @apply font-corporate;
    }
    .corporate_grid {
        @apply grid grid-cols-4 gap-4 md:grid-cols-8 md:gap-6 lg:grid-cols-12 lg:gap-8 px-4 md:px-8 lg:px-12 w-full max-w-[1280px] mx-auto;
    }

    .corporate_grid_full {
        @apply col-start-1 md:col-start-1 lg:col-start-1 col-end-5 md:col-end-9 lg:col-end-13;
    }

    .corporate_grid_halfLeft {
        @apply col-start-1 md:col-start-1 lg:col-start-1 col-end-5 md:col-end-5 lg:col-end-7;
    }

    .corporate_grid_halfRight {
        @apply col-start-1 md:col-start-5 lg:col-start-7 col-end-5 md:col-end-9 lg:col-end-13;
    }

    .corporate_grid_flex3Cols {
        @apply flex flex-col md:flex-row gap-4 md:gap-6 lg:gap-8;
    }

    .corporate_grid_flex3Cols .corporate_grid_flexCol {
        @apply w-full md:w-1/3;
    }

    /* headlines */
    .h1 {
        @apply font-corporateBlack text-h1-mobile lg:text-h1 leading-h1-mobile lg:leading-h1;
    }

    .h2 {
        @apply font-tktypeBold text-h2-mobile lg:text-h2 leading-h2-mobile lg:leading-h2;
    }

    .h3 {
        @apply font-tktypeBold text-h3-mobile lg:text-h3 leading-h3-mobile lg:leading-h3;
    }

    .subtitle {
        @apply text-subtitle-mobile lg:text-subtitle leading-subtitle-mobile lg:leading-subtitle;
    }

    p a {
        @apply underline;
    }

    .h1:has(+ .h3) {
        /* if spacing should be different for headline sets */
    }

    p {
        @apply text-normal;
        @apply leading-normal;
    }

    p.text-small {
        @apply leading-small;
    }

    p.text-extrasmall {
        @apply leading-extrasmall;
    }

    p.text-big {
        @apply leading-big;
    }

    /* only for styleguide */
    .layoutPlaceholder {
        @apply bg-[#666666] w-full text-white p-4;
    }
}
